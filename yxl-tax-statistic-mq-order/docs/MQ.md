# MQ消息队列业务逻辑文档

## 概述

本项目使用RabbitMQ作为消息队列中间件，主要用于处理订单同步、退款订单同步和商户支付渠道变更等业务场景。系统采用Spring AMQP框架进行消息队列的配置和管理。

## 架构设计

### 消息队列拓扑结构

```
订单同步流程:
Producer -> sale_list_sync_exchange (Fanout) -> sale_list_sync_queue -> Consumer
                                                      ↓ (失败重试)
                                              sale_list_dead_exchange (Direct) -> sale_list_dead_queue

退款订单同步流程:
Producer -> return_list_sync_exchange (Fanout) -> return_list_sync_queue -> Consumer

商户支付变更流程:
Producer -> shop_pay_change_exchange (Fanout) -> shop_pay_change_queue -> Consumer
```

## 队列配置

### 1. 订单同步队列配置

**配置文件**: [`src/main/java/cc/buyhoo/tax/mq/config/RabbitBuyhooConfig.java`](../src/main/java/cc/buyhoo/tax/mq/config/RabbitBuyhooConfig.java)

#### 主要队列和交换机
- **订单同步队列**: `sale_list_sync_queue`
- **订单同步交换机**: `sale_list_sync_exchange` (Fanout类型)
- **订单死信队列**: `sale_list_dead_queue`
- **订单死信交换机**: `sale_list_dead_exchange` (Direct类型)
- **死信路由键**: `sale_list_dead_routing_key`

#### 特性配置
- 队列持久化：所有队列均配置为持久化(`durable`)
- 死信机制：订单同步队列配置了死信交换机和路由键，处理失败消息

### 2. 退款订单同步队列配置

- **退款订单队列**: `return_list_sync_queue`
- **退款订单交换机**: `return_list_sync_exchange` (Fanout类型)

### 3. 商户支付变更队列配置

- **商户支付变更队列**: `shop_pay_change_queue`
- **商户支付变更交换机**: `shop_pay_change_exchange` (Fanout类型)

## 消息监听器

### 1. 订单同步监听器

**实现类**: [`src/main/java/cc/buyhoo/tax/mq/listener/SaleListListener.java`](../src/main/java/cc/buyhoo/tax/mq/listener/SaleListListener.java)

#### 主要功能
- **正常订单处理**: 监听`sale_list_sync_queue`队列，手动ACK模式
- **异常订单处理**: 监听`sale_list_dead_queue`死信队列，记录异常订单信息

#### 消息处理流程
1. 接收JSON格式的订单消息
2. 反序列化为`SaleListSubscribeParams`对象
3. 调用`SyncSaleListService.syncSaleList()`处理订单同步
4. 手动确认消息(`channel.basicAck()`)
5. 异常情况下拒绝消息(`channel.basicReject()`)

### 2. 退款订单监听器

**实现类**: [`src/main/java/cc/buyhoo/tax/mq/listener/ReturnListListener.java`](../src/main/java/cc/buyhoo/tax/mq/listener/ReturnListListener.java)

#### 主要功能
- 监听`return_list_sync_queue`队列
- 处理退款订单同步业务逻辑

## 业务服务层

### 1. 订单同步服务

**实现类**: [`src/main/java/cc/buyhoo/tax/mq/service/impl/SyncSaleListServiceImpl.java`](../src/main/java/cc/buyhoo/tax/mq/service/impl/SyncSaleListServiceImpl.java)

#### 核心业务逻辑
1. **数据验证**: 验证订单参数和商户信息
2. **订单数据处理**: 
   - 保存订单主表(`BusSaleListEntity`)
   - 保存订单明细(`BusSaleListDetailEntity`)
   - 保存支付明细(`BusSaleListPayDetailEntity`)
3. **异步处理**:
   - 生成发票数据
   - 同步会员信息
   - 创建订单临时表(用于纳统大屏实时显示)
   - 处理迁入迁出业务
4. **订单拆解**: 根据企业配置进行大额订单拆解

### 2. 退款订单同步服务

**实现类**: [`src/main/java/cc/buyhoo/tax/mq/service/impl/SyncReturnListServiceImpl.java`](../src/main/java/cc/buyhoo/tax/mq/service/impl/SyncReturnListServiceImpl.java)

#### 核心业务逻辑
1. **数据验证**: 验证退款订单参数
2. **退款数据处理**:
   - 保存退款订单主表(`BusReturnListEntity`)
   - 保存退款明细(`BusReturnListDetailEntity`)
   - 计算服务费

### 3. RabbitMQ消息发送服务

**实现类**: [`src/main/java/cc/buyhoo/tax/mq/service/impl/RabbitMqServiceImpl.java`](../src/main/java/cc/buyhoo/tax/mq/service/impl/RabbitMqServiceImpl.java)

#### 主要功能
- **商户支付渠道变更**: 发送商户支付配置变更消息到`shop_pay_change_exchange`

## 消息参数模型

### 1. 订单消息参数

**主要参数类**:
- [`SaleListParams`](../src/main/java/cc/buyhoo/tax/mq/params/SaleListParams.java): 订单基本信息
- [`SaleListSubscribeParams`](../src/main/java/cc/buyhoo/tax/mq/params/SaleListSubscribeParams.java): 订单订阅参数(包含订单、明细、支付、会员信息)

#### 关键字段
- `saleListUnique`: 订单编号
- `shopUnique`: 商店唯一标识
- `saleListTotal`: 应收金额
- `saleListActuallyReceived`: 实际收到金额
- `saleType`: 订单类型(实体店/APP/小程序/外卖等)

### 2. 退款订单消息参数

**主要参数类**:
- [`ReturnListParams`](../src/main/java/cc/buyhoo/tax/mq/params/ReturnListParams.java): 退款订单基本信息
- [`ReturnListSubscribeParams`](../src/main/java/cc/buyhoo/tax/mq/params/ReturnListSubscribeParams.java): 退款订单订阅参数

#### 关键字段
- `retListUnique`: 退款申请单号
- `saleListUnique`: 原销售订单编号
- `retListTotal`: 退货总金额
- `retListState`: 退款状态
- `retMoneyType`: 退款方式

## 错误处理机制

### 1. 死信队列处理

**错误记录服务**: [`src/main/java/cc/buyhoo/tax/mq/service/impl/BusMqErrorRecordServiceImpl.java`](../src/main/java/cc/buyhoo/tax/mq/service/impl/BusMqErrorRecordServiceImpl.java)

#### 处理流程
1. 订单处理失败后，消息自动路由到死信队列
2. 死信队列监听器接收异常消息
3. 将异常订单信息保存到`bus_mq_error_record`表
4. 记录商户编码、订单编码、创建时间等信息

### 2. 异常处理策略

- **消息确认**: 使用手动ACK模式，确保消息处理完成后才确认
- **异常重试**: 处理失败的消息会被拒绝并路由到死信队列
- **日志记录**: 详细记录处理过程和异常信息

## API接口

### 手动消息发送接口

**控制器**: [`src/main/java/cc/buyhoo/tax/mq/controller/MqCoontroller.java`](../src/main/java/cc/buyhoo/tax/mq/controller/MqCoontroller.java)

#### 接口列表
- `POST /mq/shopPayChange`: 商户支付渠道变更

## 配置说明

### 应用配置

**配置文件**: [`src/main/resources/application.yml`](../src/main/resources/application.yml)

#### 关键配置
- 服务端口: `14002`
- 上下文路径: `/taxStatisticMq`
- 应用名称: `yxl-tax-statistic-mq`
- Nacos配置: 服务发现和配置管理

### 依赖配置

**Maven配置**: [`pom.xml`](../pom.xml)

#### 主要依赖
- Spring Boot Starter AMQP: RabbitMQ集成
- Spring Cloud Alibaba: 微服务框架
- MyBatis Plus: 数据库操作
- Dubbo: RPC框架

## 业务流程图

### 订单同步流程
```
外部系统 -> MQ Producer -> sale_list_sync_exchange -> sale_list_sync_queue 
    -> SaleListListener -> SyncSaleListService -> 数据库保存 -> 异步处理
                                                        ↓ (失败)
                                                 sale_list_dead_queue -> 错误记录
```

### 商户支付变更流程
```
API调用 -> RabbitMqService -> shop_pay_change_exchange -> shop_pay_change_queue -> 外部系统消费
```

## 监控和运维

### 日志记录
- 消息接收和处理日志
- 异常处理日志
- 业务操作日志

### 性能优化
- 异步处理非关键业务
- 批量数据库操作
- 连接池配置优化

## 技术特性

### 1. 消息确认机制
- **手动ACK**: 订单同步队列使用手动确认模式，确保消息处理完成后才确认
- **自动ACK**: 退款订单队列使用自动确认模式，简化处理流程

### 2. 死信队列机制
- **自动路由**: 处理失败的消息自动路由到死信队列
- **异常记录**: 死信队列消息会被记录到数据库，便于后续排查和重处理

### 3. 异步处理优化
- **发票生成**: 使用`ThreadUtil.execAsync()`异步生成发票数据
- **会员同步**: 异步处理会员信息同步，避免阻塞主流程
- **订单拆解**: 大额订单拆解采用异步处理，提高响应速度

## Dubbo服务集成

### BusShopApi服务

**实现类**: [`src/main/java/cc/buyhoo/tax/mq/service/impl/BusShopApiImpl.java`](../src/main/java/cc/buyhoo/tax/mq/service/impl/BusShopApiImpl.java)

#### 功能说明
- 通过Dubbo RPC提供商户支付渠道变更服务
- 与HTTP接口功能相同，但通过RPC调用方式提供服务
- 使用`@DubboService`注解暴露服务

## 消息格式示例

### 订单同步消息格式
```json
{
  "saleListParams": {
    "saleListUnique": 202409050001,
    "shopUnique": 1001,
    "saleListTotal": 100.00,
    "saleListActuallyReceived": 100.00,
    "saleType": 0,
    "saleListDatetime": "2024-09-05 10:30:00"
  },
  "detailParamsList": [...],
  "payDetailParamsList": [...],
  "customerParams": {...}
}
```

### 商户支付变更消息格式
```json
[
  {
    "shopUnique": 1001,
    "mchId": "商户号",
    "mchKey": "商户密钥",
    "payType": 1,
    "otherSet": "其他配置",
    "otherSetWo": "其他配置2"
  }
]
```

## 注意事项

1. **消息幂等性**: 需要在业务层面保证消息处理的幂等性
2. **事务管理**: 订单同步服务使用`@Transactional`注解确保数据一致性
3. **资源管理**: 手动ACK模式需要正确处理Channel资源
4. **监控告警**: 建议对死信队列进行监控，及时发现处理异常
5. **配置管理**: RabbitMQ连接配置通过Nacos配置中心管理
6. **性能调优**: 根据业务量调整队列的预取数量和消费者数量
